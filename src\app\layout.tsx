import type { Metada<PERSON> } from 'next'
import { <PERSON> } from 'next/font/google'
import './globals.css'
import PerformanceMonitor from '@/components/performance/PerformanceMonitor'
import CriticalCSS from '@/components/CriticalCSS'

// Optimize font loading with Next.js font optimization
const barlow = Barlow({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'], // Reduced font weights for better performance
  display: 'swap', // Use font-display: swap for better LCP
  preload: true,
  variable: '--font-barlow',
  fallback: ['system-ui', 'arial'], // Better fallback fonts
})

export const metadata: Metadata = {
  title: 'navhaus | what matters, made real',
  description: 'Navhaus is a design and development studio that builds bold, efficient, and meaningful digital experiences — nothing more, nothing less.',
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  authors: [{ name: 'Navhaus' }],
  keywords: 'web agency, web development, wordpress, web apps, design studio',
  icons: {
    icon: '/images/icon.png',
    shortcut: '/images/icon.png',
    apple: '/images/icon.png',
  },
  other: {
    'theme-color': '#f0ebde',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={barlow.variable}>
      <head>
        {/* Critical CSS for LCP optimization */}
        <CriticalCSS />
        {/* Performance optimizations */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <meta name="format-detection" content="telephone=no" />
        <meta httpEquiv="x-ua-compatible" content="ie=edge" />
      </head>
      <body className={barlow.className}>
        {/* Only include PerformanceMonitor in development */}
        {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
        {children}
      </body>
    </html>
  )
}
