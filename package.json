{"name": "navhaus-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npm run extract-critical && next build", "build:analyze": "ANALYZE=true npm run build", "start": "next start", "lint": "next lint", "extract-critical": "node scripts/extract-critical-css.js", "perf:measure": "node scripts/measure-lcp.js"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}