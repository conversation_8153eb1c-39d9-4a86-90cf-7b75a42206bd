import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Only apply optimizations in production
  if (process.env.NODE_ENV === 'production') {
    // Set cache headers for static assets
    if (request.nextUrl.pathname.startsWith('/_next/static/')) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }

    // Set cache headers for images
    if (request.nextUrl.pathname.startsWith('/images/')) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }

    // Set cache headers for fonts
    if (request.nextUrl.pathname.startsWith('/fonts/')) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }

    // Set cache headers for CSS files
    if (request.nextUrl.pathname.endsWith('.css')) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    }

    // Performance headers for all pages
    response.headers.set('X-DNS-Prefetch-Control', 'on')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
    
    // Remove cache-control: no-store for main pages to enable bfcache
    if (!request.nextUrl.pathname.startsWith('/_next/') && 
        !request.nextUrl.pathname.startsWith('/api/')) {
      response.headers.delete('Cache-Control')
      // Set appropriate cache headers for pages
      response.headers.set('Cache-Control', 'public, max-age=0, must-revalidate')
    }
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/webpack-hmr (webpack hot reload)
     */
    '/((?!api|_next/webpack-hmr).*)',
  ],
}
