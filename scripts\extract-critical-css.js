#!/usr/bin/env node

/**
 * Critical CSS extraction script for Navhaus
 * This script extracts critical above-the-fold CSS for better LCP performance
 */

const fs = require('fs')
const path = require('path')

// Critical CSS that should be inlined
const criticalCSS = `
/* Critical CSS - Inline for LCP optimization */
html {
  font-family: var(--font-barlow), system-ui, arial, sans-serif;
  font-display: swap;
}

body {
  color: #000000;
  background-color: #f0ebde;
  font-family: var(--font-barlow), system-ui, arial, sans-serif;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* Critical hero styles for LCP */
.text-hero {
  font-size: 4rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
  font-weight: 700;
  font-family: var(--font-barlow), system-ui, arial, sans-serif;
  font-display: swap;
}

/* Critical responsive breakpoints */
@media (max-width: 768px) {
  .text-hero {
    font-size: 2.5rem;
  }
}

@media (max-width: 640px) {
  .text-hero {
    font-size: 2rem;
  }
}

/* Critical navigation and header styles */
nav, header {
  font-family: var(--font-barlow), system-ui, arial, sans-serif;
}

/* Critical button styles */
.btn-primary {
  padding: 0.875rem 1.5rem;
  border: 2px solid #000000;
  background-color: transparent;
  color: #000000;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.2s ease;
  border-radius: 1rem;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
}

.btn-primary:hover {
  background-color: #000000;
  color: #f0ebde;
}

/* Critical layout utilities */
.container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.uppercase {
  text-transform: uppercase;
}

/* Critical spacing utilities */
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-8 { margin-top: 2rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }

/* Critical grid utilities */
.grid {
  display: grid;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

/* Critical visibility utilities */
.hidden {
  display: none;
}

@media (min-width: 768px) {
  .md\\:block {
    display: block;
  }
  .md\\:hidden {
    display: none;
  }
}
`

// Write critical CSS to a file that can be inlined
const outputPath = path.join(process.cwd(), 'public', 'critical.css')
fs.writeFileSync(outputPath, criticalCSS.trim())

console.log('✅ Critical CSS extracted to public/critical.css')
console.log('💡 This CSS should be inlined in the <head> for optimal LCP performance')

// Also create a minified version
const minifiedCSS = criticalCSS
  .replace(/\/\*[^*]*\*+(?:[^/*][^*]*\*+)*\//g, '') // Remove comments
  .replace(/\s+/g, ' ') // Collapse whitespace
  .replace(/;\s*}/g, '}') // Remove unnecessary semicolons
  .replace(/\s*{\s*/g, '{') // Clean up braces
  .replace(/;\s*/g, ';') // Clean up semicolons
  .trim()

const minifiedPath = path.join(process.cwd(), 'public', 'critical.min.css')
fs.writeFileSync(minifiedPath, minifiedCSS)

console.log('✅ Minified critical CSS extracted to public/critical.min.css')
console.log(`📊 Size reduction: ${criticalCSS.length} → ${minifiedCSS.length} bytes (${Math.round((1 - minifiedCSS.length / criticalCSS.length) * 100)}% smaller)`)
