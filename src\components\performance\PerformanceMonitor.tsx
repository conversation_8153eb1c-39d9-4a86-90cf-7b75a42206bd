'use client'

import { useEffect } from 'react'

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV !== 'development') return

    let lcpObserver: PerformanceObserver | null = null
    let clsObserver: PerformanceObserver | null = null

    // Monitor LCP with proper cleanup
    try {
      lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any

        if (lastEntry) {
          console.log('🚀 LCP:', Math.round(lastEntry.startTime), 'ms')
          if (lastEntry.element) {
            console.log('LCP Element:', lastEntry.element)
          }
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      console.log('LCP monitoring not supported')
    }

    // Monitor CLS with proper cleanup
    try {
      clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
          }
        }
        if (clsValue > 0) {
          console.log('📐 CLS:', clsValue.toFixed(4))
        }
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (e) {
      console.log('CLS monitoring not supported')
    }

    // Cleanup function to prevent memory leaks and bfcache issues
    return () => {
      if (lcpObserver) {
        lcpObserver.disconnect()
        lcpObserver = null
      }
      if (clsObserver) {
        clsObserver.disconnect()
        clsObserver = null
      }
    }
  }, [])

  return null
}
